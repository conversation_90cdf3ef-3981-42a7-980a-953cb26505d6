<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据面板 - 小说运营数据分析系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/common.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        /* 增强的响应式布局样式 */
        .dashboard-container {
            padding: 0 15px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .dashboard-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: none;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            border-radius: 12px;
            overflow: hidden;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
        }

        .dashboard-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        /* 优化的指标卡片样式 */
        .metric-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border: 1px solid rgba(102, 126, 234, 0.1);
            border-radius: 12px;
            padding: 24px 20px;
            margin-bottom: 20px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            position: relative;
            overflow: hidden;
        }

        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .metric-card:hover {
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.15);
            transform: translateY(-3px);
            border-color: rgba(102, 126, 234, 0.2);
        }

        .metric-title {
            font-size: 13px;
            color: #6c757d;
            margin-bottom: 12px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 6px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .metric-value {
            font-size: 32px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 10px;
            line-height: 1;
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
        }

        .metric-change {
            font-size: 13px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 4px 8px;
            border-radius: 20px;
            width: fit-content;
        }

        .metric-change.positive {
            color: #27ae60;
            background: rgba(39, 174, 96, 0.1);
        }

        .metric-change.negative {
            color: #e74c3c;
            background: rgba(231, 76, 60, 0.1);
        }

        .metric-change.neutral {
            color: #95a5a6;
            background: rgba(149, 165, 166, 0.1);
        }

        .metric-icon {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        /* 优化的字数完成率卡片 */
        .word-completion-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border: 1px solid rgba(102, 126, 234, 0.1);
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            position: relative;
            overflow: hidden;
        }

        .word-completion-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .word-completion-title {
            font-size: 15px;
            color: #2c3e50;
            margin-bottom: 20px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .word-completion-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 20px;
        }

        .word-completion-item {
            text-align: center;
            padding: 16px 12px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            transition: all 0.3s ease;
            border: 1px solid rgba(102, 126, 234, 0.05);
        }

        .word-completion-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
            background: rgba(255, 255, 255, 1);
        }

        .word-completion-label {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 8px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.3px;
        }

        .word-completion-value {
            font-size: 18px;
            font-weight: 700;
            color: #667eea;
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
        }

        /* 图表容器优化 */
        .chart-container {
            position: relative;
            height: 350px;
            margin: 24px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 8px;
        }

        .data-loading {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 40px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            margin: 20px 0;
        }

        /* 页面标题区域优化 */
        .page-title-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 16px;
            padding: 32px;
            margin-bottom: 32px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
        }

        .page-title-section h4 {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
        }

        .page-title-section p {
            font-size: 16px;
            opacity: 0.9;
            margin: 0;
            font-weight: 400;
        }

        /* 控制面板区域 */
        .control-panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 32px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.08);
            border: 1px solid rgba(102, 126, 234, 0.1);
        }

        /* 响应式优化 */
        @media (max-width: 1200px) {
            .dashboard-container {
                padding: 0 20px;
            }

            .word-completion-grid {
                grid-template-columns: repeat(3, 1fr);
                gap: 16px;
            }
        }

        @media (max-width: 992px) {
            .metric-value {
                font-size: 28px;
            }

            .word-completion-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 14px;
            }

            .chart-container {
                height: 300px;
                padding: 16px;
            }
        }

        @media (max-width: 768px) {
            .dashboard-container {
                padding: 0 15px;
            }

            .metric-card {
                padding: 20px 16px;
                margin-bottom: 16px;
            }

            .metric-value {
                font-size: 24px;
            }

            .word-completion-card {
                padding: 20px;
            }

            .word-completion-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 12px;
            }

            .word-completion-item {
                padding: 12px 8px;
            }

            .page-title-section {
                padding: 24px 20px;
                margin-bottom: 24px;
            }

            .page-title-section h4 {
                font-size: 24px;
            }

            .control-panel {
                padding: 20px;
                margin-bottom: 24px;
            }
        }

        @media (max-width: 576px) {
            .dashboard-container {
                padding: 0 12px;
            }

            .metric-card {
                padding: 16px 12px;
            }

            .metric-value {
                font-size: 22px;
            }

            .word-completion-grid {
                grid-template-columns: 1fr;
                gap: 10px;
            }

            .word-completion-item {
                padding: 10px;
            }

            .chart-container {
                height: 250px;
                padding: 12px;
            }

            .page-title-section {
                padding: 20px 16px;
                margin-bottom: 20px;
            }

            .page-title-section h4 {
                font-size: 20px;
                flex-direction: column;
                gap: 8px;
            }
        }

        /* 表格响应式优化 */
        .table-responsive {
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.08);
            border: 1px solid rgba(102, 126, 234, 0.1);
        }

        .table {
            margin-bottom: 0;
        }

        .table th {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: none;
            font-weight: 600;
            color: #2c3e50;
            padding: 16px 12px;
            font-size: 13px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .table td {
            border: none;
            padding: 14px 12px;
            border-bottom: 1px solid rgba(102, 126, 234, 0.05);
            font-size: 14px;
        }

        .table tbody tr:hover {
            background: rgba(102, 126, 234, 0.03);
        }

        /* 快速操作按钮优化 */
        .quick-actions .btn {
            border-radius: 10px;
            font-weight: 600;
            padding: 12px 20px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 13px;
        }

        .quick-actions .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line"></i> 数据面板
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="/">首页</a>
                    <a class="nav-link active" href="dashboard.html">数据面板</a>
                    <a class="nav-link" href="prediction.html">AI预测</a>
                    <a class="nav-link" href="reports.html">报表导出</a>
                    <a class="nav-link" href="data-management.html">数据管理</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="dashboard-container mt-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="page-title-section">
                    <h4><i class="fas fa-tachometer-alt"></i> 数据面板</h4>
                    <p>实时监控小说运营数据，关键指标展示，趋势分析图表</p>
                </div>
            </div>
        </div>

        <!-- 小说选择和设置 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="control-panel">
                    <div class="row">
                        <div class="col-lg-6 col-md-12 mb-3 mb-lg-0">
                            <h5 class="card-title mb-3"><i class="fas fa-book"></i> 选择小说</h5>
                            <div class="mb-3">
                                <label for="novelSelect" class="form-label">选择小说</label>
                                <select class="form-select" id="novelSelect">
                                    <option value="">加载中...</option>
                                </select>
                            </div>
                            <button class="btn btn-primary" onclick="loadDashboard()">
                                <i class="fas fa-sync"></i> 加载数据
                            </button>
                        </div>
                        <div class="col-lg-6 col-md-12">
                            <h5 class="card-title mb-3"><i class="fas fa-cog"></i> 分析设置</h5>
                            <div class="mb-3">
                                <label for="daysSelect" class="form-label">分析天数</label>
                                <select class="form-select" id="daysSelect">
                                    <option value="7">最近7天</option>
                                    <option value="15">最近15天</option>
                                    <option value="30" selected>最近30天</option>
                                    <option value="60">最近60天</option>
                                </select>
                            </div>
                            <button class="btn btn-outline-primary" onclick="refreshData()">
                                <i class="fas fa-refresh"></i> 刷新数据
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 加载状态 -->
        <div class="loading" id="loading">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2">正在分析数据，请稍候...</p>
        </div>

        <!-- 数据面板 -->
        <div id="dashboard" style="display: none;">
            <!-- 数据说明 -->
            <div class="row mb-3">
                <div class="col-12">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>数据说明：</strong>以下指标显示的是该小说最新记录的实际运营数据，包括阅读人数、在读人数、作品评分、评论次数、加书架人数、催更人数、追更人数、章节读完率、章节跟读率、字数读完率分段(10万-100万字)及流量构成分析
                        <span id="latestDataDate" class="ms-2 badge bg-primary"></span>
                    </div>
                </div>
            </div>

            <!-- 关键指标 -->
            <div class="row mb-4">
                <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6 mb-3">
                    <div class="metric-card">
                        <div class="metric-title">
                            <span class="metric-icon"></span>
                            阅读人数
                        </div>
                        <div class="metric-value" id="totalReads">-</div>
                        <div class="metric-change neutral" id="totalReadsChange">
                            比前日 --
                        </div>
                    </div>
                </div>
                <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6 mb-3">
                    <div class="metric-card">
                        <div class="metric-title">
                            <span class="metric-icon"></span>
                            在读人数
                        </div>
                        <div class="metric-value" id="activeReaders">-</div>
                        <div class="metric-change neutral" id="activeReadersChange">
                            比前日 --
                        </div>
                    </div>
                </div>
                <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6 mb-3">
                    <div class="metric-card">
                        <div class="metric-title">
                            <span class="metric-icon"></span>
                            作品评分
                        </div>
                        <div class="metric-value" id="avgRating">-</div>
                        <div class="metric-change neutral" id="avgRatingChange">
                            比前日 持平
                        </div>
                    </div>
                </div>
                <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6 mb-3">
                    <div class="metric-card">
                        <div class="metric-title">
                            <span class="metric-icon"></span>
                            评论次数
                        </div>
                        <div class="metric-value" id="totalComments">-</div>
                        <div class="metric-change neutral" id="totalCommentsChange">
                            比前日 --
                        </div>
                    </div>
                </div>
                <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6 mb-3">
                    <div class="metric-card">
                        <div class="metric-title">
                            <span class="metric-icon"></span>
                            加书架人数
                        </div>
                        <div class="metric-value" id="bookmarkCount">-</div>
                        <div class="metric-change neutral" id="bookmarkCountChange">
                            比前日 --
                        </div>
                    </div>
                </div>
                <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6 mb-3">
                    <div class="metric-card">
                        <div class="metric-title">
                            <span class="metric-icon"></span>
                            催更人数
                        </div>
                        <div class="metric-value" id="urgeUpdateCount">-</div>
                        <div class="metric-change neutral" id="urgeUpdateCountChange">
                            比前日 --
                        </div>
                    </div>
                </div>
            </div>

            <!-- 第二行指标 -->
            <div class="row mb-4">
                <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6 mb-3">
                    <div class="metric-card">
                        <div class="metric-title">
                            <span class="metric-icon"></span>
                            追更人数
                        </div>
                        <div class="metric-value" id="followCount">-</div>
                        <div class="metric-change neutral" id="followCountChange">
                            比前日 --
                        </div>
                    </div>
                </div>
                <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6 mb-3">
                    <div class="metric-card">
                        <div class="metric-title">
                            <span class="metric-icon"></span>
                            章节读完率
                        </div>
                        <div class="metric-value" id="chapterCompletionRate">-</div>
                        <div class="metric-change neutral" id="chapterCompletionRateChange">
                            比前日 --
                        </div>
                    </div>
                </div>
                <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6 mb-3">
                    <div class="metric-card">
                        <div class="metric-title">
                            <span class="metric-icon"></span>
                            章节跟读率
                        </div>
                        <div class="metric-value" id="chapterFollowRate">-</div>
                        <div class="metric-change neutral" id="chapterFollowRateChange">
                            比前日 --
                        </div>
                    </div>
                </div>
                <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6 mb-3">
                    <div class="metric-card">
                        <div class="metric-title">
                            <span class="metric-icon"></span>
                            正常状态天数
                        </div>
                        <div class="metric-value" id="normalStatus">-</div>
                        <div class="metric-change neutral" id="normalStatusChange">
                            比前日 --
                        </div>
                    </div>
                </div>
                <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6 mb-3">
                    <div class="metric-card">
                        <div class="metric-title">
                            <span class="metric-icon"></span>
                            总流量
                        </div>
                        <div class="metric-value" id="totalTraffic">-</div>
                        <div class="metric-change neutral" id="totalTrafficChange">
                            比前日 --
                        </div>
                    </div>
                </div>
            </div>

            <!-- 字数读完率分段 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="word-completion-card">
                        <div class="word-completion-title">
                            <span class="metric-icon"></span>
                            字数读完率分段
                        </div>
                        <div class="word-completion-grid">
                            <div class="word-completion-item">
                                <div class="word-completion-label">10万字</div>
                                <div class="word-completion-value" id="wordCompletion10w">0.0%</div>
                            </div>
                            <div class="word-completion-item">
                                <div class="word-completion-label">20万字</div>
                                <div class="word-completion-value" id="wordCompletion20w">0.0%</div>
                            </div>
                            <div class="word-completion-item">
                                <div class="word-completion-label">30万字</div>
                                <div class="word-completion-value" id="wordCompletion30w">0.0%</div>
                            </div>
                            <div class="word-completion-item">
                                <div class="word-completion-label">50万字</div>
                                <div class="word-completion-value" id="wordCompletion50w">0.0%</div>
                            </div>
                            <div class="word-completion-item">
                                <div class="word-completion-label">80万字</div>
                                <div class="word-completion-value" id="wordCompletion80w">0.0%</div>
                            </div>
                            <div class="word-completion-item">
                                <div class="word-completion-label">100万字</div>
                                <div class="word-completion-value" id="wordCompletion100w">0.0%</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 图表区域 -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card dashboard-card">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fas fa-chart-line"></i> 阅读量趋势</h5>
                            <div class="chart-container">
                                <canvas id="readChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card dashboard-card">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fas fa-chart-pie"></i> 最新流量构成</h5>
                            <div class="chart-container">
                                <canvas id="trafficChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 完成率趋势和分析 -->
            <div class="row mb-4">
                <div class="col-md-8">
                    <div class="card dashboard-card">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fas fa-percentage"></i> 完成率趋势分析</h5>
                            <div class="chart-container">
                                <canvas id="completionChart"></canvas>
                            </div>
                            <div id="wordCompletionAnalysis" class="mt-3">
                                <small class="text-muted">选择小说后显示完成率趋势分析</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card dashboard-card">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fas fa-info-circle"></i> 数据分析</h5>
                            <div id="trendInfo">
                                <p class="text-muted">加载数据后显示趋势分析</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 详细数据表格 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card dashboard-card">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fas fa-table"></i> 详细数据</h5>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover table-sm">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>日期</th>
                                            <th>状态</th>
                                            <th>阅读人数</th>
                                            <th>在读人数</th>
                                            <th>作品评分</th>
                                            <th>评论次数</th>
                                            <th>加书架</th>
                                            <th>催更</th>
                                            <th>追更</th>
                                            <th>章节读完率</th>
                                            <th>章节跟读率</th>
                                            <th>10万字读完率</th>
                                            <th>20万字读完率</th>
                                            <th>30万字读完率</th>
                                            <th>50万字读完率</th>
                                            <th>80万字读完率</th>
                                            <th>100万字读完率</th>
                                            <th>流量构成</th>
                                        </tr>
                                    </thead>
                                    <tbody id="dataTableBody">
                                        <tr>
                                            <td colspan="18" class="text-center text-muted">请选择小说并加载数据</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 快速操作 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card dashboard-card">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fas fa-bolt"></i> 快速操作</h5>
                            <div class="row quick-actions">
                                <div class="col-lg-3 col-md-6 mb-3">
                                    <a href="prediction.html" class="btn btn-success w-100">
                                        <i class="fas fa-robot"></i> AI预测分析
                                    </a>
                                </div>
                                <div class="col-lg-3 col-md-6 mb-3">
                                    <a href="reports.html" class="btn btn-info w-100">
                                        <i class="fas fa-download"></i> 导出报表
                                    </a>
                                </div>
                                <div class="col-lg-3 col-md-6 mb-3">
                                    <a href="data-management.html" class="btn btn-warning w-100">
                                        <i class="fas fa-plus"></i> 添加数据
                                    </a>
                                </div>
                                <div class="col-lg-3 col-md-6 mb-3">
                                    <button class="btn btn-secondary w-100" onclick="refreshData()">
                                        <i class="fas fa-sync"></i> 刷新数据
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/common.js"></script>
    <script src="js/dashboard.js"></script>
</body>
</html>
